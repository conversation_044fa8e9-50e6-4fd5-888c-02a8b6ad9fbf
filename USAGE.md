# DRV8833 驱动使用指南

## 快速开始

### 1. 添加依赖

在你的 `Cargo.toml` 中添加：

```toml
[dependencies]
drv8833 = { path = "path/to/drv8833" }
embassy-stm32 = { git = "https://github.com/embassy-rs/embassy.git", features = ["stm32f103c8"] }
embedded-hal = "1.0"
```

### 2. 使用 SimplePwmChannel

这是推荐的使用方式，完全兼容 Embassy：

```rust
use drv8833::{Drv8833, Motor, Direction};
use embassy_stm32::gpio::{Level, Output, Speed};
use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
use embassy_stm32::time::Hertz;

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    let p = embassy_stm32::init(Config::default());

    // 初始化GPIO引脚
    let ain1 = Output::new(p.PA0, Level::Low, Speed::Low);
    let ain2 = Output::new(p.PA1, Level::Low, Speed::Low);
    let bin1 = Output::new(p.PA2, Level::Low, Speed::Low);
    let bin2 = Output::new(p.PA3, Level::Low, Speed::Low);

    // 初始化PWM
    let ch1 = PwmPin::new_ch1(p.PA6, embassy_stm32::gpio::OutputType::PushPull);
    let ch2 = PwmPin::new_ch2(p.PA7, embassy_stm32::gpio::OutputType::PushPull);
    let mut pwm = SimplePwm::new(
        p.TIM3, 
        Some(ch1), 
        Some(ch2), 
        None, 
        None, 
        Hertz(10_000), 
        Default::default()
    );
    
    pwm.enable(timer::Channel::Ch1);
    pwm.enable(timer::Channel::Ch2);

    // 获取PWM通道
    let pwm_a = pwm.ch1();
    let pwm_b = pwm.ch2();

    // 创建驱动实例
    let mut motor_driver = Drv8833::new(ain1, ain2, bin1, bin2, pwm_a, pwm_b);

    // 控制电机
    loop {
        // 电机A前进，电机B后退，50%速度
        motor_driver.set_motor(Motor::A, Direction::Forward, 50).unwrap();
        motor_driver.set_motor(Motor::B, Direction::Backward, 50).unwrap();
        Timer::after(Duration::from_secs(2)).await;

        // 停止所有电机
        motor_driver.stop_all().unwrap();
        Timer::after(Duration::from_secs(1)).await;
    }
}
```

### 3. 单电机控制

如果你只需要控制一个电机：

```rust
use drv8833::{Drv8833Single, Direction};

// 初始化
let ain1 = Output::new(p.PA0, Level::Low, Speed::Low);
let ain2 = Output::new(p.PA1, Level::Low, Speed::Low);
let ch1 = PwmPin::new_ch1(p.PA6, embassy_stm32::gpio::OutputType::PushPull);
let mut pwm = SimplePwm::new(p.TIM3, Some(ch1), None, None, None, Hertz(10_000), Default::default());
pwm.enable(timer::Channel::Ch1);
let pwm_ch1 = pwm.ch1();

// 创建单电机驱动
let mut motor = Drv8833Single::new(ain1, ain2, pwm_ch1);

// 控制电机
motor.set_motor(Direction::Forward, 75).unwrap();  // 75%速度前进
motor.set_motor(Direction::Backward, 50).unwrap(); // 50%速度后退
motor.brake().unwrap();                            // 刹车
motor.stop().unwrap();                             // 滑行停止
```

## 关键特性

### 方向控制
- `Direction::Forward`: 正转
- `Direction::Backward`: 反转
- `Direction::Brake`: 刹车（两个输入都为高）
- `Direction::Coast`: 滑行（两个输入都为低）

### 速度控制
- 使用 0-100 的百分比值
- 自动转换为对应的 PWM 占空比

### 错误处理
- 所有操作都返回 `Result` 类型
- 支持类型安全的错误处理

## 硬件连接

```
STM32F103    DRV8833
---------    -------
PA0      ->  AIN1
PA1      ->  AIN2
PA2      ->  BIN1
PA3      ->  BIN2
PA6      ->  PWM A (TIM3_CH1)
PA7      ->  PWM B (TIM3_CH2)
3.3V     ->  VCC
GND      ->  GND
```

## 运行示例

```bash
# 基本API演示
cargo run --example simple_demo

# Embassy完整示例（需要硬件）
cargo run --example embassy_simple_pwm --features embassy
cargo run --example embassy_single_motor --features embassy
```
