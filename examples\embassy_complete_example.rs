//! Complete Embassy DRV8833 Mecanum Drive Example
//!
//! This example demonstrates the complete setup and usage of the DRV8833 mecanum drive system:
//! - **2 DRV8833 chips** controlling **4 mecanum wheels**
//! - Embassy async framework with STM32F103
//! - Full omnidirectional movement capabilities
//! - PID-based angle control for MPU integration

#![no_std]
#![no_main]

use defmt::*;
use defmt_rtt as _;
use embassy_executor::Spawner;
use embassy_stm32::gpio::{Level, Output, Speed};
use embassy_stm32::time::Hertz;
use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
use embassy_stm32::{Config, bind_interrupts, peripherals, timer};
use embassy_time::{Duration, Timer};

use drv8833::Drv8833;
use drv8833::mecanum::{MecanumDirection, MecanumDrive};

bind_interrupts!(struct Irqs {
    TIM3 => timer::InterruptHandler<peripherals::TIM3>;
    TIM4 => timer::InterruptHandler<peripherals::TIM4>;
});

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    let p = embassy_stm32::init(Config::default());
    info!("Starting DRV8833 Mecanum Drive Example");

    // Configure GPIO pins for motor control
    // Front driver (TIM3) - controls FL (Motor A) and FR (Motor B)
    let front_ain1 = Output::new(p.PA0, Level::Low, Speed::Low); // FL direction 1
    let front_ain2 = Output::new(p.PA1, Level::Low, Speed::Low); // FL direction 2
    let front_bin1 = Output::new(p.PA2, Level::Low, Speed::Low); // FR direction 1
    let front_bin2 = Output::new(p.PA3, Level::Low, Speed::Low); // FR direction 2

    // Back driver (TIM4) - controls BL (Motor A) and BR (Motor B)
    let back_ain1 = Output::new(p.PA4, Level::Low, Speed::Low); // BL direction 1
    let back_ain2 = Output::new(p.PA5, Level::Low, Speed::Low); // BL direction 2
    let back_bin1 = Output::new(p.PA8, Level::Low, Speed::Low); // BR direction 1
    let back_bin2 = Output::new(p.PA9, Level::Low, Speed::Low); // BR direction 2

    // Configure PWM for speed control
    let front_ch1 = PwmPin::new_ch1(p.PA6, embassy_stm32::gpio::Pull::None);
    let front_ch2 = PwmPin::new_ch2(p.PA7, embassy_stm32::gpio::Pull::None);
    let back_ch1 = PwmPin::new_ch1(p.PB0, embassy_stm32::gpio::Pull::None);
    let back_ch2 = PwmPin::new_ch2(p.PB1, embassy_stm32::gpio::Pull::None);

    // Create PWM instances
    let mut front_pwm = SimplePwm::new(
        p.TIM3,
        Some(front_ch1),
        Some(front_ch2),
        None,
        None,
        Hertz(10_000),
        Default::default(),
    );
    front_pwm.enable_all();

    let mut back_pwm = SimplePwm::new(
        p.TIM4,
        Some(back_ch1),
        Some(back_ch2),
        None,
        None,
        Hertz(10_000),
        Default::default(),
    );
    back_pwm.enable_all();

    // Get PWM channels
    let front_pwm_a = front_pwm.ch1();
    let front_pwm_b = front_pwm.ch2();
    let back_pwm_a = back_pwm.ch1();
    let back_pwm_b = back_pwm.ch2();

    // Create DRV8833 drivers
    let front_driver = Drv8833::new(
        front_ain1,
        front_ain2,
        front_bin1,
        front_bin2,
        front_pwm_a,
        front_pwm_b,
    );

    let back_driver = Drv8833::new(
        back_ain1, back_ain2, back_bin1, back_bin2, back_pwm_a, back_pwm_b,
    );

    // Create mecanum drive system
    let mut mecanum = MecanumDrive::new(front_driver, back_driver);

    info!("Mecanum drive initialized successfully!");

    // Demo movement sequence
    loop {
        info!("Moving forward");
        let _ = mecanum.move_direction(MecanumDirection::Forward, 50);
        Timer::after(Duration::from_secs(2)).await;

        info!("Moving backward");
        let _ = mecanum.move_direction(MecanumDirection::Backward, 50);
        Timer::after(Duration::from_secs(2)).await;

        info!("Strafing left");
        let _ = mecanum.move_direction(MecanumDirection::Left, 50);
        Timer::after(Duration::from_secs(2)).await;

        info!("Strafing right");
        let _ = mecanum.move_direction(MecanumDirection::Right, 50);
        Timer::after(Duration::from_secs(2)).await;

        info!("Rotating clockwise");
        let _ = mecanum.move_direction(MecanumDirection::RotateClockwise, 50);
        Timer::after(Duration::from_secs(2)).await;

        info!("Rotating counter-clockwise");
        let _ = mecanum.move_direction(MecanumDirection::RotateCounterClockwise, 50);
        Timer::after(Duration::from_secs(2)).await;

        info!("Stopping");
        let _ = mecanum.stop_all();
        Timer::after(Duration::from_secs(1)).await;

        // Advanced movement: diagonal with rotation
        info!("Advanced movement: forward-right with clockwise rotation");
        let _ = mecanum.move_with_rotation(30, 50, 15); // x=30 (right), y=50 (forward), rotation=15 (clockwise)
        Timer::after(Duration::from_secs(3)).await;

        let _ = mecanum.stop_all();
        Timer::after(Duration::from_secs(2)).await;

        // Angle correction example (simulating MPU feedback)
        info!("Angle correction example");
        let mut yaw_deviation = 10.0; // Simulated 10 degree deviation
        let mut correction_count = 0;

        while !mecanum.correct_yaw_deviation(yaw_deviation, 30) && correction_count < 10 {
            Timer::after(Duration::from_millis(100)).await;
            yaw_deviation -= 1.0; // Simulate correction
            correction_count += 1;
            info!("Correcting yaw deviation: {:.1} degrees", yaw_deviation);
        }

        info!("Yaw correction complete");
        Timer::after(Duration::from_secs(2)).await;
    }
}

// Example of how to use PID angle control with MPU6050 (pseudo-code)
/*
use drv8833::mecanum::PidAngleConfig;

async fn mpu_angle_control_example(
    mut mecanum: MecanumDrive<peripherals::TIM3, peripherals::TIM4>,
    mut mpu: Mpu6050<I2c<'static, peripherals::I2C1>>,
) {
    // Create PID configuration for angle control
    let mut pid_config = PidAngleConfig::new(
        0.0,   // current_angle (will be updated from MPU)
        90.0,  // target_angle: 90 degrees
        80,    // max_speed
        1.0,   // kp - proportional gain
        0.1,   // ki - integral gain
        0.05,  // kd - derivative gain
    );

    loop {
        // Read current angle from MPU6050
        let current_angle = mpu.get_yaw().await.unwrap_or(0.0);
        pid_config.update_current_angle(current_angle);

        // Use PID control to rotate to target angle
        let reached_target = mecanum.rotate_to_angle(&mut pid_config);

        if reached_target {
            info!("Target angle reached!");
            break;
        }

        Timer::after(Duration::from_millis(50)).await;
    }
}
*/
