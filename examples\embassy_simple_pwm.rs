//! Embassy SimplePwmChannel example for DRV8833 driver
//! 
//! This example shows how to use the DRV8833 driver with Embassy's SimplePwmChannel.
//! This is a complete working example for STM32F103.

#![no_std]
#![no_main]

use defmt::*;
use embassy_executor::Spawner;
use embassy_stm32::gpio::{Level, Output, Speed};
use embassy_stm32::time::Hertz;
use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
use embassy_stm32::{bind_interrupts, peripherals, timer, Config};
use embassy_time::{Duration, Timer};
use drv8833::{Direction, Drv8833, Motor};
use {defmt_rtt as _, panic_probe as _};

bind_interrupts!(struct Irqs {
    TIM3 => timer::InterruptHandler<peripherals::TIM3>;
});

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    let mut config = Config::default();
    {
        use embassy_stm32::rcc::*;
        config.rcc.hse = Some(Hse {
            freq: Hertz(8_000_000),
            mode: HseMode::Oscillator,
        });
        config.rcc.pll = Some(Pll {
            src: PllSource::HSE,
            prediv: PllPreDiv::DIV1,
            mul: PllMul::MUL9,
        });
        config.rcc.sys = Sysclk::PLL1_P;
        config.rcc.ahb_pre = AHBPrescaler::DIV1;
        config.rcc.apb1_pre = APBPrescaler::DIV2;
        config.rcc.apb2_pre = APBPrescaler::DIV1;
    }
    let p = embassy_stm32::init(config);

    info!("Starting DRV8833 with Embassy SimplePwmChannel example");

    // Initialize GPIO pins for motor control
    let ain1 = Output::new(p.PA0, Level::Low, Speed::Low);
    let ain2 = Output::new(p.PA1, Level::Low, Speed::Low);
    let bin1 = Output::new(p.PA2, Level::Low, Speed::Low);
    let bin2 = Output::new(p.PA3, Level::Low, Speed::Low);

    // Initialize PWM for speed control
    let ch1 = PwmPin::new_ch1(p.PA6, embassy_stm32::gpio::OutputType::PushPull);
    let ch2 = PwmPin::new_ch2(p.PA7, embassy_stm32::gpio::OutputType::PushPull);
    let mut pwm = SimplePwm::new(
        p.TIM3, 
        Some(ch1), 
        Some(ch2), 
        None, 
        None, 
        Hertz(10_000), 
        Default::default()
    );
    
    // Enable PWM channels
    pwm.enable(timer::Channel::Ch1);
    pwm.enable(timer::Channel::Ch2);

    // Get individual PWM channels - these implement SetDutyCycle trait
    let pwm_a = pwm.ch1();
    let pwm_b = pwm.ch2();

    // Create DRV8833 driver instance
    let mut motor_driver = Drv8833::new(ain1, ain2, bin1, bin2, pwm_a, pwm_b);

    info!("DRV8833 initialized with SimplePwmChannel, starting motor control demo");

    loop {
        info!("Motor A forward, Motor B backward - 50% speed");
        motor_driver.set_motor(Motor::A, Direction::Forward, 50).unwrap();
        motor_driver.set_motor(Motor::B, Direction::Backward, 50).unwrap();
        Timer::after(Duration::from_secs(2)).await;

        info!("Motor A backward, Motor B forward - 75% speed");
        motor_driver.set_motor(Motor::A, Direction::Backward, 75).unwrap();
        motor_driver.set_motor(Motor::B, Direction::Forward, 75).unwrap();
        Timer::after(Duration::from_secs(2)).await;

        info!("Both motors forward - 100% speed");
        motor_driver.set_motor(Motor::A, Direction::Forward, 100).unwrap();
        motor_driver.set_motor(Motor::B, Direction::Forward, 100).unwrap();
        Timer::after(Duration::from_secs(2)).await;

        info!("Braking both motors");
        motor_driver.brake_all().unwrap();
        Timer::after(Duration::from_secs(1)).await;

        info!("Stopping both motors (coast)");
        motor_driver.stop_all().unwrap();
        Timer::after(Duration::from_secs(2)).await;
    }
}
