//! Simple demonstration of DRV8833 driver API
//!
//! This example shows how to use the DRV8833 driver with mock implementations.
//! Replace the mock types with actual Embassy GPIO and PWM types for real usage.

use drv8833::{Direction, Drv8833, Drv8833<PERSON><PERSON><PERSON>, Motor};

// Mock implementations for demonstration
struct MockPin;
struct MockPwm {
    max_duty: u16,
}

impl embedded_hal::digital::OutputPin for MockPin {
    fn set_low(&mut self) -> Result<(), Self::Error> {
        Ok(())
    }
    fn set_high(&mut self) -> Result<(), Self::Error> {
        Ok(())
    }
}

impl embedded_hal::digital::ErrorType for MockPin {
    type Error = core::convert::Infallible;
}

impl embedded_hal::pwm::SetDutyCycle for MockPwm {
    fn max_duty_cycle(&self) -> u16 {
        self.max_duty
    }
    fn set_duty_cycle(&mut self, _duty: u16) -> Result<(), Self::Error> {
        Ok(())
    }
}

impl embedded_hal::pwm::ErrorType for MockPwm {
    type Error = core::convert::Infallible;
}

fn dual_motor_example() {
    // Create mock pins and PWM channels
    let ain1 = MockPin;
    let ain2 = MockPin;
    let bin1 = MockPin;
    let bin2 = MockPin;
    let pwm_a = MockPwm { max_duty: 1000 };
    let pwm_b = MockPwm { max_duty: 1000 };

    // Create DRV8833 driver instance
    let mut motor_driver = Drv8833::new(ain1, ain2, bin1, bin2, pwm_a, pwm_b);

    // Control motors - these operations cannot fail with Infallible error type
    let _ = motor_driver.set_motor(Motor::A, Direction::Forward, 50);
    let _ = motor_driver.set_motor(Motor::B, Direction::Backward, 75);
    let _ = motor_driver.brake_all();
    let _ = motor_driver.stop_all();
}

fn single_motor_example() {
    // Create mock pins and PWM channel
    let in1 = MockPin;
    let in2 = MockPin;
    let pwm = MockPwm { max_duty: 1000 };

    // Create single motor driver instance
    let mut motor = Drv8833Single::new(in1, in2, pwm);

    // Control motor - these operations cannot fail with Infallible error type
    let _ = motor.set_motor(Direction::Forward, 100);
    let _ = motor.set_motor(Direction::Backward, 50);
    let _ = motor.brake();
    let _ = motor.stop();
}

fn main() {
    println!("DRV8833 Driver Demo");

    println!("Running dual motor example...");
    dual_motor_example();

    println!("Running single motor example...");
    single_motor_example();

    println!("Demo completed successfully!");
}
