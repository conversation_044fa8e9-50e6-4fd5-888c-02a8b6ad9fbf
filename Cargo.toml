[package]
name = "motor_encoder"
version = "0.1.0"
edition = "2024"


[dependencies]
embassy-stm32 = { version = "0.2.0", features = [
    "defmt",
    "stm32f103c8",
    "unstable-pac",
    "memory-x",
    "time-driver-any",

], git = "https://github.com/embassy-rs/embassy.git", optional = true }
embassy-executor = { git = "https://github.com/embassy-rs/embassy.git", features = [
    "arch-cortex-m",
    "executor-thread",
    "defmt",
], optional = true }
embassy-time = { git = "https://github.com/embassy-rs/embassy.git", features = [
    "defmt",
    "defmt-timestamp-uptime",
    "tick-hz-32_768",
], optional = true }


defmt = { version = "1.0.1", optional = true }
defmt-rtt = { version = "1.0.0", optional = true }
cortex-m = { version = "0.7", features = ["inline-asm"], optional = true }
cortex-m-rt = { version = "0.7", optional = true }

[features]
default = ["embassy"]
embassy = [
    "embassy-stm32",
    "embassy-executor",
    "embassy-time",
    "defmt",
    "defmt-rtt",
    "cortex-m",
    "cortex-m-rt",
]

[[example]]
name = "embassy_complete_example"
required-features = ["embassy"]

[lib]
test = false
bench = false
