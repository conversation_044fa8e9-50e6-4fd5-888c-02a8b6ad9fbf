//! Mecanum wheel drive demonstration
//! 
//! This example shows how to use the MecanumDrive with four DRV8833Single drivers.

use drv8833::{Direction, Drv8833Single};
use drv8833::mecanum::{MecanumDrive, MecanumDirection, MecanumError};

// Mock implementations for demonstration
struct MockPin;
struct MockPwm { max_duty: u16 }

impl embedded_hal::digital::OutputPin for MockPin {
    fn set_low(&mut self) -> Result<(), Self::Error> { Ok(()) }
    fn set_high(&mut self) -> Result<(), Self::Error> { Ok(()) }
}

impl embedded_hal::digital::ErrorType for MockPin {
    type Error = core::convert::Infallible;
}

impl embedded_hal::pwm::SetDutyCycle for MockPwm {
    fn max_duty_cycle(&self) -> u16 { self.max_duty }
    fn set_duty_cycle(&mut self, _duty: u16) -> Result<(), Self::Error> { Ok(()) }
}

impl embedded_hal::pwm::ErrorType for MockPwm {
    type Error = core::convert::Infallible;
}

fn mecanum_basic_movements() -> Result<(), MecanumError> {
    println!("Creating mecanum drive system...");
    
    // Create four motor drivers
    let fl_motor = Drv8833Single::new(MockPin, MockPin, MockPwm { max_duty: 1000 });
    let fr_motor = Drv8833Single::new(MockPin, MockPin, MockPwm { max_duty: 1000 });
    let bl_motor = Drv8833Single::new(MockPin, MockPin, MockPwm { max_duty: 1000 });
    let br_motor = Drv8833Single::new(MockPin, MockPin, MockPwm { max_duty: 1000 });
    
    // Create mecanum drive
    let mut mecanum = MecanumDrive::new(fl_motor, fr_motor, bl_motor, br_motor);
    
    println!("Testing basic movements:");
    
    // Forward movement
    println!("  - Moving forward at 50% speed");
    mecanum.move_direction(MecanumDirection::Forward, 50)?;
    
    // Backward movement
    println!("  - Moving backward at 50% speed");
    mecanum.move_direction(MecanumDirection::Backward, 50)?;
    
    // Strafe left
    println!("  - Strafing left at 75% speed");
    mecanum.move_direction(MecanumDirection::Left, 75)?;
    
    // Strafe right
    println!("  - Strafing right at 75% speed");
    mecanum.move_direction(MecanumDirection::Right, 75)?;
    
    // Rotate clockwise
    println!("  - Rotating clockwise at 60% speed");
    mecanum.move_direction(MecanumDirection::RotateClockwise, 60)?;
    
    // Rotate counter-clockwise
    println!("  - Rotating counter-clockwise at 60% speed");
    mecanum.move_direction(MecanumDirection::RotateCounterClockwise, 60)?;
    
    // Stop
    println!("  - Stopping all motors");
    mecanum.move_direction(MecanumDirection::Stop, 0)?;
    
    Ok(())
}

fn mecanum_advanced_movements() -> Result<(), MecanumError> {
    println!("\nTesting advanced movements:");
    
    // Create mecanum drive
    let fl_motor = Drv8833Single::new(MockPin, MockPin, MockPwm { max_duty: 1000 });
    let fr_motor = Drv8833Single::new(MockPin, MockPin, MockPwm { max_duty: 1000 });
    let bl_motor = Drv8833Single::new(MockPin, MockPin, MockPwm { max_duty: 1000 });
    let br_motor = Drv8833Single::new(MockPin, MockPin, MockPwm { max_duty: 1000 });
    let mut mecanum = MecanumDrive::new(fl_motor, fr_motor, bl_motor, br_motor);
    
    // Individual wheel control
    println!("  - Individual wheel control (FL:50, FR:-30, BL:70, BR:-20)");
    mecanum.set_individual_wheels(50, -30, 70, -20)?;
    
    // Combined movement: forward + right strafe
    println!("  - Combined movement: forward + right strafe");
    mecanum.move_with_rotation(30, 50, 0)?;  // x=30 (right), y=50 (forward), rotation=0
    
    // Combined movement: left strafe + clockwise rotation
    println!("  - Combined movement: left strafe + clockwise rotation");
    mecanum.move_with_rotation(-40, 0, 25)?;  // x=-40 (left), y=0, rotation=25 (clockwise)
    
    // Diagonal movement: forward-right + counter-clockwise rotation
    println!("  - Diagonal movement: forward-right + counter-clockwise rotation");
    mecanum.move_with_rotation(60, 60, -20)?;  // x=60, y=60, rotation=-20 (counter-clockwise)
    
    // Stop all
    println!("  - Stopping all motors");
    mecanum.stop_all()?;
    
    // Brake all
    println!("  - Braking all motors");
    mecanum.brake_all()?;
    
    Ok(())
}

fn main() {
    println!("DRV8833 Mecanum Drive Demo");
    println!("==========================");
    
    match mecanum_basic_movements() {
        Ok(()) => println!("✓ Basic movements completed successfully"),
        Err(e) => println!("✗ Basic movements failed: {:?}", e),
    }
    
    match mecanum_advanced_movements() {
        Ok(()) => println!("✓ Advanced movements completed successfully"),
        Err(e) => println!("✗ Advanced movements failed: {:?}", e),
    }
    
    println!("\nMecanum Drive Features:");
    println!("- Omnidirectional movement (forward, backward, left, right)");
    println!("- In-place rotation (clockwise, counter-clockwise)");
    println!("- Individual wheel speed control");
    println!("- Combined translation and rotation");
    println!("- Mecanum wheel kinematics calculations");
    
    println!("\nHardware Setup:");
    println!("Connect four DRV8833 drivers to control FL, FR, BL, BR motors");
    println!("Each motor needs 2 direction pins + 1 PWM pin");
    println!("Total: 8 direction pins + 4 PWM pins");
}
