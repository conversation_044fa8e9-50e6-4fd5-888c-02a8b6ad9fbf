//! Embassy Mecanum wheel drive example
//! 
//! This example shows how to use the MecanumDrive with Embassy and SimplePwmChannel.
//! This is a code template - adjust pin assignments for your hardware.

use drv8833::{Drv8833Single};
use drv8833::mecanum::{MecanumDrive, MecanumDirection};

fn embassy_mecanum_example() {
    println!("Embassy Mecanum Drive Setup Guide");
    println!("=================================");
    
    /*
    // In a real Embassy project, you would set it up like this:
    
    use embassy_stm32::gpio::{Level, Output, Speed};
    use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
    use embassy_stm32::time::Hertz;
    use embassy_time::{Duration, Timer};
    
    #[embassy_executor::main]
    async fn main(_spawner: Spawner) {
        let p = embassy_stm32::init(Config::default());

        // Initialize GPIO pins for four motors
        // Front Left Motor (FL)
        let fl_in1 = Output::new(p.PA0, Level::Low, Speed::Low);
        let fl_in2 = Output::new(p.PA1, Level::Low, Speed::Low);
        
        // Front Right Motor (FR)
        let fr_in1 = Output::new(p.PA2, Level::Low, Speed::Low);
        let fr_in2 = Output::new(p.PA3, Level::Low, Speed::Low);
        
        // Back Left Motor (BL)
        let bl_in1 = Output::new(p.PA4, Level::Low, Speed::Low);
        let bl_in2 = Output::new(p.PA5, Level::Low, Speed::Low);
        
        // Back Right Motor (BR)
        let br_in1 = Output::new(p.PA8, Level::Low, Speed::Low);
        let br_in2 = Output::new(p.PA9, Level::Low, Speed::Low);

        // Initialize PWM channels for four motors
        let fl_pwm_pin = PwmPin::new(p.PA6, embassy_stm32::gpio::OutputType::PushPull);
        let fr_pwm_pin = PwmPin::new(p.PA7, embassy_stm32::gpio::OutputType::PushPull);
        let bl_pwm_pin = PwmPin::new(p.PB0, embassy_stm32::gpio::OutputType::PushPull);
        let br_pwm_pin = PwmPin::new(p.PB1, embassy_stm32::gpio::OutputType::PushPull);
        
        // Create PWM instances
        let mut pwm_tim3 = SimplePwm::new(
            p.TIM3, 
            Some(fl_pwm_pin), 
            Some(fr_pwm_pin), 
            None, 
            None, 
            Hertz(10_000), 
            Default::default()
        );
        
        let mut pwm_tim4 = SimplePwm::new(
            p.TIM4, 
            Some(bl_pwm_pin), 
            Some(br_pwm_pin), 
            None, 
            None, 
            Hertz(10_000), 
            Default::default()
        );
        
        // Get PWM channels
        let fl_pwm = pwm_tim3.ch1();
        let fr_pwm = pwm_tim3.ch2();
        let bl_pwm = pwm_tim4.ch1();
        let br_pwm = pwm_tim4.ch2();

        // Create individual motor drivers
        let fl_motor = Drv8833Single::new(fl_in1, fl_in2, fl_pwm);
        let fr_motor = Drv8833Single::new(fr_in1, fr_in2, fr_pwm);
        let bl_motor = Drv8833Single::new(bl_in1, bl_in2, bl_pwm);
        let br_motor = Drv8833Single::new(br_in1, br_in2, br_pwm);

        // Create mecanum drive system
        let mut mecanum = MecanumDrive::new(fl_motor, fr_motor, bl_motor, br_motor);

        info!("Mecanum drive initialized, starting movement demo");

        loop {
            // Forward movement
            info!("Moving forward");
            mecanum.move_direction(MecanumDirection::Forward, 50).unwrap();
            Timer::after(Duration::from_secs(2)).await;

            // Strafe right
            info!("Strafing right");
            mecanum.move_direction(MecanumDirection::Right, 50).unwrap();
            Timer::after(Duration::from_secs(2)).await;

            // Backward movement
            info!("Moving backward");
            mecanum.move_direction(MecanumDirection::Backward, 50).unwrap();
            Timer::after(Duration::from_secs(2)).await;

            // Strafe left
            info!("Strafing left");
            mecanum.move_direction(MecanumDirection::Left, 50).unwrap();
            Timer::after(Duration::from_secs(2)).await;

            // Rotate clockwise
            info!("Rotating clockwise");
            mecanum.move_direction(MecanumDirection::RotateClockwise, 40).unwrap();
            Timer::after(Duration::from_secs(2)).await;

            // Rotate counter-clockwise
            info!("Rotating counter-clockwise");
            mecanum.move_direction(MecanumDirection::RotateCounterClockwise, 40).unwrap();
            Timer::after(Duration::from_secs(2)).await;

            // Combined movement: forward + right + slight rotation
            info!("Combined movement: forward-right with rotation");
            mecanum.move_with_rotation(30, 50, 15).unwrap();
            Timer::after(Duration::from_secs(2)).await;

            // Stop
            info!("Stopping");
            mecanum.stop_all().unwrap();
            Timer::after(Duration::from_secs(2)).await;
        }
    }
    */
    
    println!("Key points for Embassy integration:");
    println!("1. Use four separate DRV8833Single drivers");
    println!("2. Each motor needs 2 GPIO pins + 1 PWM channel");
    println!("3. Total hardware: 8 GPIO pins + 4 PWM channels");
    println!("4. Use SimplePwm with multiple timers if needed");
    println!("5. PWM channels automatically implement SetDutyCycle trait");
}

fn hardware_connection_guide() {
    println!("\nHardware Connection Guide:");
    println!("=========================");
    println!("STM32F103    DRV8833 #1 (FL)    DRV8833 #2 (FR)    DRV8833 #3 (BL)    DRV8833 #4 (BR)");
    println!("---------    -------------    -------------    -------------    -------------");
    println!("PA0      ->  AIN1");
    println!("PA1      ->  AIN2");
    println!("PA2      ->                   AIN1");
    println!("PA3      ->                   AIN2");
    println!("PA4      ->                                    AIN1");
    println!("PA5      ->                                    AIN2");
    println!("PA8      ->                                                     AIN1");
    println!("PA9      ->                                                     AIN2");
    println!("PA6      ->  PWM (TIM3_CH1)");
    println!("PA7      ->                   PWM (TIM3_CH2)");
    println!("PB0      ->                                    PWM (TIM4_CH1)");
    println!("PB1      ->                                                     PWM (TIM4_CH2)");
    println!("3.3V     ->  VCC             VCC             VCC             VCC");
    println!("GND      ->  GND             GND             GND             GND");
    
    println!("\nMotor Connections:");
    println!("- FL Motor -> DRV8833 #1 AOUT1/AOUT2");
    println!("- FR Motor -> DRV8833 #2 AOUT1/AOUT2");
    println!("- BL Motor -> DRV8833 #3 AOUT1/AOUT2");
    println!("- BR Motor -> DRV8833 #4 AOUT1/AOUT2");
}

fn mecanum_kinematics_explanation() {
    println!("\nMecanum Wheel Kinematics:");
    println!("=========================");
    println!("For movement with velocity (x, y) and rotation (r):");
    println!("- FL wheel speed = y - x - r");
    println!("- FR wheel speed = y + x + r");
    println!("- BL wheel speed = y + x - r");
    println!("- BR wheel speed = y - x + r");
    println!();
    println!("Movement patterns:");
    println!("- Forward:  FL=+, FR=+, BL=+, BR=+");
    println!("- Backward: FL=-, FR=-, BL=-, BR=-");
    println!("- Right:    FL=+, FR=-, BL=-, BR=+");
    println!("- Left:     FL=-, FR=+, BL=+, BR=-");
    println!("- CW Rot:   FL=+, FR=-, BL=+, BR=-");
    println!("- CCW Rot:  FL=-, FR=+, BL=-, BR=+");
}

fn main() {
    embassy_mecanum_example();
    hardware_connection_guide();
    mecanum_kinematics_explanation();
}
