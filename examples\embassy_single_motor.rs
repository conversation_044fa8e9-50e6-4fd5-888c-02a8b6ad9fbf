//! Embassy SimplePwmChannel single motor example for DRV8833 driver
//! 
//! This example shows how to use the DRV8833Single driver with Embassy's SimplePwmChannel.

#![no_std]
#![no_main]

use defmt::*;
use embassy_executor::Spawner;
use embassy_stm32::gpio::{Level, Output, Speed};
use embassy_stm32::time::Hertz;
use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
use embassy_stm32::{bind_interrupts, peripherals, timer, Config};
use embassy_time::{Duration, Timer};
use drv8833::{Direction, Drv8833Single};
use {defmt_rtt as _, panic_probe as _};

bind_interrupts!(struct Irqs {
    TIM3 => timer::InterruptHandler<peripherals::TIM3>;
});

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    let mut config = Config::default();
    {
        use embassy_stm32::rcc::*;
        config.rcc.hse = Some(Hse {
            freq: Hertz(8_000_000),
            mode: HseMode::Oscillator,
        });
        config.rcc.pll = Some(Pll {
            src: PllSource::HSE,
            prediv: PllPreDiv::DIV1,
            mul: PllMul::MUL9,
        });
        config.rcc.sys = Sysclk::PLL1_P;
        config.rcc.ahb_pre = AHBPrescaler::DIV1;
        config.rcc.apb1_pre = APBPrescaler::DIV2;
        config.rcc.apb2_pre = APBPrescaler::DIV1;
    }
    let p = embassy_stm32::init(config);

    info!("Starting DRV8833 single motor with Embassy SimplePwmChannel example");

    // Initialize GPIO pins for single motor control (Motor A)
    let ain1 = Output::new(p.PA0, Level::Low, Speed::Low);
    let ain2 = Output::new(p.PA1, Level::Low, Speed::Low);

    // Initialize PWM for speed control
    let ch1 = PwmPin::new_ch1(p.PA6, embassy_stm32::gpio::OutputType::PushPull);
    let mut pwm = SimplePwm::new(
        p.TIM3, 
        Some(ch1), 
        None, 
        None, 
        None, 
        Hertz(10_000), 
        Default::default()
    );
    
    // Enable PWM channel
    pwm.enable(timer::Channel::Ch1);

    // Get PWM channel - this implements SetDutyCycle trait
    let pwm_ch1 = pwm.ch1();

    // Create single motor DRV8833 driver instance
    let mut motor = Drv8833Single::new(ain1, ain2, pwm_ch1);

    info!("Single motor DRV8833 initialized with SimplePwmChannel, starting control demo");

    loop {
        info!("Motor forward - gradually increasing speed");
        for speed in (10..=100).step_by(10) {
            info!("Speed: {}%", speed);
            motor.set_motor(Direction::Forward, speed).unwrap();
            Timer::after(Duration::from_millis(500)).await;
        }

        info!("Motor backward - gradually increasing speed");
        for speed in (10..=100).step_by(10) {
            info!("Speed: {}%", speed);
            motor.set_motor(Direction::Backward, speed).unwrap();
            Timer::after(Duration::from_millis(500)).await;
        }

        info!("Braking motor");
        motor.brake().unwrap();
        Timer::after(Duration::from_secs(1)).await;

        info!("Stopping motor (coast)");
        motor.stop().unwrap();
        Timer::after(Duration::from_secs(2)).await;
    }
}
